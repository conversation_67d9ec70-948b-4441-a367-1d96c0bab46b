<?php
namespace api\autodesk\customers;
use autodesk_api\autodesk_api;
use Edge\Edge;

include_once(FS_FUNCTIONS . DIRECTORY_SEPARATOR . 'subscriptions.fn.php');


function search($p) {
    return generate_customer_table(criteria: ["search" => $p['search_terms'], "limit" => 50],just_body: true);
}

/* data_table_filter($p) {
    return generate_customer_table(data_table_filter($p), just_body: false);
}*/

function data_table_filter($p) {
    print_rr($p, 'subs_api_data_table_filter');
    $cols = $criteria = [];
    if (isset($p['column'])) {
        foreach ($p['column'] as $column => $value) {
            if (empty($value)) continue;
            print_rr($column, ' val: ' . $value);
            $col_parts = explode("_", $column,2 );
            $table = $col_parts[0];
            $column_name = $col_parts[1];
            $cols["{$table}.{$column_name}"] = ['=', $value];
        }
        if (count($cols) > 0) $criteria["where"] = $cols;
    }
    if (isset($p['search_terms']) && $p['search_terms'] != '')  $criteria["search"] = $p['search_terms'];
    return generate_customer_table(criteria:$criteria, just_body: false);
}

if (file_exists("resources/functions/subscriptions.fn.php")) include_once("resources/functions/subscriptions.fn.php");
if (file_exists("resources/functions/email_history.fn.php")) include_once("resources/functions/email_history.fn.php");

function oldview($p) {
    // print_rr($subscription);
    $autodesk = new autodesk_api();
    $customer = $autodesk->customers->get($p['csn']);
    print_rr($customer,'custarrr');
    $out = tcs_autogen_datadisplay_card('Customer', tcs_autogen_datadisplay_section($customer, 'Customer Details', true), true);
    $out .= subscriptions\generate_subscription_table([
        "column" => ['subs.endCustomer_id','=',$customer['id'] ]
    ]);
   $out .= generate_customer_history_feed($customer['account_csn']);
    return $out;
}



function view($p){
    $autodesk = new autodesk_api();
    $matcher = new \subscription_matcher();

    if (!isset($p['csn'])) {
        echo '<div class="text-center py-8"><p class="text-gray-500">No customer CSN provided.</p></div>';
        return;
    }

    // Get all customer data columns we need for the unified display
    $customer_columns = [
        // Customer details
        'endcust_id',
        'endcust_account_csn',
        'endcust_name',
        'endcust_account_type',
        'endcust_individual_flag',
        'endcust_named_account_flag',
        'endcust_named_account_group',
        'endcust_parent_industry_group',
        'endcust_parent_industry_segment',
        'endcust_team_id',
        'endcust_team_name',

        // Contact information
        'endcust_primary_admin_first_name',
        'endcust_primary_admin_last_name',
        'endcust_primary_admin_email',
        'endcust_first_name',
        'endcust_last_name',
        'endcust_email',

        // Address information
        'endcust_address1',
        'endcust_address2',
        'endcust_address3',
        'endcust_city',
        'endcust_state_province',
        'endcust_postal_code',
        'endcust_country',
        'endcust_state_province_code',
        'endcust_country_code',

        // System fields
        'endcust_created_at',
        'endcust_last_modified'
    ];

    // Get customer data
    $customer = $autodesk->customers->get($customer_columns, ['where' => ['endcust.account_csn' => ['=', $p['csn']]]]);

    if (empty($customer)) {
        echo '<div class="text-center py-8"><p class="text-gray-500">Customer not found.</p></div>';
        return;
    }

    // Try to match with manual entries
    $matched_data = [];
    try {
        $matches = $matcher->match_subscription_data($customer);
        if (!empty($matches)) {
            // Use the highest confidence match
            $matched_data = $matches[0];
        }
    } catch (Exception $e) {
        // Continue without matching data
    }

    // Get customer's subscriptions from Autodesk API
    $autodesk_subscriptions = [];
    try {
        $subscription_columns = [
            'subs_id',
            'subs_subscriptionId',
            'subs_subscriptionReferenceNumber',
            'subs_status',
            'subs_startDate',
            'subs_endDate',
            'subs_offeringName',
            'subs_quantity'
        ];

        $autodesk_subscriptions = $autodesk->subscriptions->get_all(
            $subscription_columns,
            ['where' => ['subs.endCustomer_csn' => ['=', $p['csn']]], 'limit' => 10]
        );

        // Add source information to Autodesk subscriptions
        foreach ($autodesk_subscriptions as &$subscription) {
            $subscription['data_source'] = 'autodesk';
            $subscription['source_table'] = 'autodesk_subscriptions';
            $subscription['can_add_to_unity'] = false; // Already in main system
        }
    } catch (Exception $e) {
        // Continue without Autodesk subscriptions
    }

    // Get subscriptions from CSV tables using subscription_matcher
    $csv_subscriptions = [];
    try {
        // Use subscription_matcher to find CSV entries that match this customer
        $search_criteria = [
            'limit' => 1000  // Increased limit to get all records
        ];

        $csv_entries = $matcher->get_all_manual_entries($search_criteria);

        tcs_log("Customer matching - Found " . count($csv_entries) . " CSV entries to check", 'customer_matching');
        tcs_log("Customer data: email=" . ($customer['endcust_primary_admin_email'] ?? 'none') . ", name=" . ($customer['endcust_name'] ?? 'none'), 'customer_matching');

        // Filter for entries that are likely subscriptions and match this customer
        foreach ($csv_entries as $entry) {
            // Check if this entry matches our customer
            $is_match = false;
            $match_type = '';
            $match_confidence = 0;

            // Get customer email and name for matching
            $customer_email = strtolower($customer['endcust_primary_admin_email'] ?? '');
            $customer_name = strtolower($customer['endcust_name'] ?? '');

            // Use configurable rules for email matching
            $email_rule = \system\subscription_matching_rules::get_rule('email_matching');
            if ($email_rule && $email_rule['enabled']) {
                foreach ($email_rule['field_patterns'] as $field) {
                    if (!empty($customer_email) && !empty($entry[$field])) {
                        $entry_email = $email_rule['case_sensitive'] ? $entry[$field] : strtolower($entry[$field]);
                        $customer_email_compare = $email_rule['case_sensitive'] ? $customer['endcust_primary_admin_email'] : $customer_email;

                        tcs_log("Comparing emails: customer='{$customer_email_compare}' vs entry[{$field}]='{$entry_email}'", 'customer_matching');

                        if ($customer_email_compare === $entry_email) {
                            $is_match = true;
                            $match_type = 'email';
                            $match_confidence = $email_rule['confidence_score'] ?? 100;
                            tcs_log("EMAIL MATCH FOUND! Field: {$field}, Confidence: {$match_confidence}%", 'customer_matching');
                            break;
                        }
                    }
                }
            }

            // Use configurable rules for company name matching
            if (!$is_match) {
                $company_rule = \system\subscription_matching_rules::get_rule('company_name_matching');
                if ($company_rule && $company_rule['enabled']) {
                    foreach ($company_rule['field_patterns'] as $field) {
                        if (!empty($customer_name) && !empty($entry[$field])) {
                            $entry_company = $company_rule['case_sensitive'] ? $entry[$field] : strtolower($entry[$field]);
                            $customer_name_compare = $company_rule['case_sensitive'] ? $customer['endcust_name'] : $customer_name;

                            // Apply preprocessing if configured
                            if (isset($company_rule['preprocessing'])) {
                                $entry_company = self::preprocess_company_name($entry_company, $company_rule['preprocessing']);
                                $customer_name_compare = self::preprocess_company_name($customer_name_compare, $company_rule['preprocessing']);
                            }

                            $similarity = similar_text($customer_name_compare, $entry_company, $percent);

                            tcs_log("Comparing companies: customer='{$customer_name_compare}' vs entry[{$field}]='{$entry_company}' (similarity: {$percent}%)", 'customer_matching');

                            $threshold = $company_rule['similarity_threshold'] ?? 70;
                            if ($percent >= $threshold) {
                                $is_match = true;
                                $match_type = 'company';
                                $match_confidence = $percent;
                                tcs_log("COMPANY MATCH FOUND! Field: {$field}, Confidence: {$percent}%", 'customer_matching');
                                break;
                            }
                        }
                    }
                }
            }

            if ($is_match) {
                // Add matching metadata
                $entry['match_type'] = $match_type;
                $entry['match_confidence'] = $match_confidence;
                $entry['data_source'] = 'csv_table';
                $csv_subscriptions[] = $entry;
                tcs_log("Added matched entry: " . json_encode(['match_type' => $match_type, 'confidence' => $match_confidence]), 'customer_matching');
            }
        }

        tcs_log("Final result: Found " . count($csv_subscriptions) . " matching CSV subscriptions", 'customer_matching');
    } catch (Exception $e) {
        tcs_log("Error in CSV subscription matching: " . $e->getMessage(), 'customer_matching');
        // Continue without CSV subscriptions
    }

    // Combine all subscriptions
    $subscriptions = array_merge($autodesk_subscriptions, $csv_subscriptions);

    // Get history data
    $hist_columns = [
        'hist_id',
        'hist_target',
        'hist_target_reference',
        'hist_customer_csn',
        'hist_date',
        'hist_media',
        'hist_message',
        'hist_user_id',
        'hist_email_address',
        'hist_triggered_by',
        'hist_result',
        'users_name'
    ];

    $where_hist = ['hist.customer_csn' => ['=', $customer['endcust_account_csn']]];
    $history = $autodesk->customers->get_history($customer['endcust_account_csn'], $hist_columns, ['where' => $where_hist]);

    // Render the new unified display
    $out = '<!-- Modal -->';
    print_rr($customer, 'customer_data');
    $out .= Edge::render('view-customer_display', [
        'customer' => $customer,
        'matched_data' => $matched_data,
        'subscriptions' => $subscriptions,
        'histdata' => $history
    ]);
    echo $out;
}

/**
 * Preprocess company name for better matching
 */
function preprocess_company_name($name, $preprocessing_rules) {
    if (isset($preprocessing_rules['remove_common_words'])) {
        $common_words = $preprocessing_rules['remove_common_words'];
        foreach ($common_words as $word) {
            $name = preg_replace('/\b' . preg_quote($word, '/') . '\b/i', '', $name);
        }
    }

    if (isset($preprocessing_rules['remove_punctuation']) && $preprocessing_rules['remove_punctuation']) {
        $name = preg_replace('/[^\w\s]/', '', $name);
    }

    if (isset($preprocessing_rules['normalize_spaces']) && $preprocessing_rules['normalize_spaces']) {
        $name = preg_replace('/\s+/', ' ', trim($name));
    }

    return $name;
}

/*

function view($p) {
    // print_rr($subscription);
    $autodesk = new autodesk_api();
    $customer = $autodesk->customers->get($input_params['csn']);
    $out .= tcs_autogen_datadisplay_card('Customer', tcs_autogen_datadisplay_section($customer, 'Customer Details', true), true);
    $subs = $autodesk->subscriptions->get_all(
        [
            "where" => [
                "columns" => [
                    "endCustomer_id" => $customer['id']
                ]
            ]
        ]
    );

    $out .= generate_subscription_table_html($subs, false, ["customer"]);

}





// Function to generate a text input
function generateTextInput($label, $name, $value) {

    //  <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Name</label>
    //  <input type="text" name="name" id="name" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" placeholder="Jane Smith">

    return "
    <div class='relative '>
        <label class='absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900' for='{$name}'>{$label}</label>
        <input type='text' id='{$name}' name='{$name}' value='{$value}' readonly
               class='block w-full rounded-md border-0 pl-1.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6'>
    </div>";
}

// Function to generate a card with collapsible sections for arrays
function generateCard($title, $content, $open = false) {
    return "
    <div x-data='{ open: false}' class='bg-white shadow-md rounded-lg col-span-4 '>
        <button @click='open = !open' class='w-full px-4 py-2 text-left text-lg font-semibold text-gray-700 hover:bg-gray-100 focus:outline-none'>
            {$title}
            <span x-show='!open' class='ml-2'>+</span>
            <span x-show='open' class='ml-2'>-</span>
        </button>
        <div x-show='open' class='px-4 py-2 gap-4 border-t border-gray-200 grid grid-cols-4'>
            {$content}
        </div>
    </div>";
}



// Function to generate a form section for associative arrays
function generateSection($data, $sectionTitle) {
    $content = '';
    $hasContent = false;
    // print_rr($data, "generate section");
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            $temp_content = generateCard(ucwords(str_replace('_', ' ', $key)), generateSection($value, $key));
            if (!$temp_content) continue;
            $content .= $temp_content;
            $hasContent = true;
        } else {
            if (empty($value)) continue;
            $content .= generateTextInput(ucwords(str_replace('_', ' ', $key)), $key, $value);
            $hasContent = true;
        }
    }
    if ($hasContent) return $content;
    else return false;
}


/*
function subscriptions_api_import_csv_into_database() {
    autodesk_api::import_csv_into_database(AutodeskSubscriptions::get_subscription_column_mapping(), DIR_FS_CATALOG . DIRECTORY_SEPARATOR . "feeds/subscriptions.csv");
}



function subscriptions_api_emails_send_reminders() {

    $autodesk = new autodesk_api();

    $subs = $autodesk->subscriptions->get_renewable();
    $file_path = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/views/subscriptions/email_history/reminder_email.view.php';
    $email_template = file_get_contents($file_path);
    if ($email_template === false) {
        throw new Exception("Failed to load email template from {$file_path}");
    }

    // Split the content by lines
    $lines = explode("\n", $email_template);

    $email_rules = explode(',', autodesk_api::database_get_storage('subscription_renew_email_send_rules'));
    $settings_days = autodesk_api::database_get_storage('subscription_renew_email_send_days');
    $settings_time = autodesk_api::database_get_storage('subscription_renew_email_send_time');


    // Check if it's time to send an email.
    foreach ($subs as $sub) {
        if ($subs['tcs_unsubscribe'] = 1) continue;
        $now = new DateTime();
        $end_date = new DateTime($sub['endDate']);
        $date_last_sent = $sub['last_email_sent_date'] ?? $sub['startDate'];
        $last_sent = new DateTime($date_last_sent);
        $days_remaining = $now->diff($end_date)->days;
        $days_since_last_sent = $now->diff($last_sent)->days;
        echo "Processing subscription " . $sub['subscriptionReferenceNumber'] . " id " . $sub['id'] . " for customer: " . $sub['endCustomer_name'] . " End date is " . $sub['endDate'] . " with d/r: {$days_remaining} and last sent: " . $sub['last_email_sent_date'] . ": ";
        foreach ($email_rules as $key => $rule) {
            if ($days_remaining <= $rule) {
                echo " is below rule $rule.<br> " . PHP_EOL;
                if ((($days_remaining + $days_since_last_sent) > $rule)) {
                    echo "sending email <br>" . PHP_EOL;
                    $autodesk->subscriptions->send_reminder_email($sub['id']);
                    //print_rr($autodesk->subscriptions->send_reminder_email($sub['id'], $rule));
                }
                break;
            }
        }
    }
}
function subscriptions_api_view($p) {
    $autodesk = new autodesk_api();
    $subscription = $autodesk->subscription->get($p['subscription_number']);
    print_rr($subscription);

    $out = '<!-- Modal -->';
    $out .= generateCard('Subscription', generateSection($subscription['body']['subscriptions'][0], 'Subscription Details'));
    echo $out;
}

    // Function to generate a text input
    function generateTextInput($label, $name, $value) {

        //  <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Name</label>
        //  <input type="text" name="name" id="name" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" placeholder="Jane Smith">

        return "
    <div class='relative '>
        <label class='absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900' for='{$name}'>{$label}</label>
        <input type='text' id='{$name}' name='{$name}' value='{$value}' readonly
               class='block w-full rounded-md border-0 pl-1.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6'>
    </div>";
    }

// Function to generate a card with collapsible sections for arrays
function generateCard($title, $content) {
    return "
    <div x-data='{ open: false }' class='bg-white shadow-md rounded-lg col-span-4 '>
        <button @click='open = !open' class='w-full px-4 py-2 text-left text-lg font-semibold text-gray-700 hover:bg-gray-100 focus:outline-none'>
            {$title}
            <span x-show='!open' class='ml-2'>+</span>
            <span x-show='open' class='ml-2'>-</span>
        </button>
        <div x-show='open' class='px-4 py-2 gap-4 border-t border-gray-200 grid grid-cols-4'>
            {$content}
        </div>
    </div>";
}



// Function to generate a form section for associative arrays
function generateSection($data, $sectionTitle) {
    $content = '';
    $hasContent = false;
    print_rr($data, "generate section");
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            $temp_content = generateCard(ucwords(str_replace('_', ' ', $key)), generateSection($value, $key));
            if (!$temp_content) continue;
            $content .= $temp_content;
            $hasContent = true;
        } else {
            if (empty($value)) continue;
            $content .= generateTextInput(ucwords(str_replace('_', ' ', $key)), $key, $value);
            $hasContent = true;
        }
    }
    if ($hasContent) return $content;
    else return false;
}
*/