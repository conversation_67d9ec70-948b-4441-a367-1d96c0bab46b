<?php

namespace system;

/**
 * Unified Field Mapper
 * 
 * Provides intelligent field mapping and normalization for CSV imports and subscription matching.
 * This centralizes all field mapping logic so it can be used consistently across the system.
 */
class unified_field_mapper {
    
    private static $log_target = 'unified_field_mapper';
    
    /**
     * Master field mapping configuration
     * Maps various field name patterns to standardized field names
     */
    private static $field_mappings = [
        // Company/Organization names
        'company_name' => [
            'patterns' => ['company', 'company_name', 'customer', 'customer_name', 'client', 'client_name', 
                          'account_name', 'end_customer_name', 'endcust_name', 'organization', 'organization_name',
                          'business_name', 'firm_name', 'sold_to_name', 'vendor_name'],
            'normalized_fields' => ['company_name', 'endcust_name', 'end_customer_name']
        ],
        
        // Email addresses
        'email' => [
            'patterns' => ['email', 'email_address', 'contact_email', 'admin_email', 'customer_email', 
                          'end_customer_contact_email', 'subscription_contact_email', 'endcust_primary_admin_email',
                          'primary_email', 'business_email', 'work_email'],
            'normalized_fields' => ['email_address', 'endcust_primary_admin_email', 'end_customer_contact_email']
        ],
        
        // Contact names
        'contact_name' => [
            'patterns' => ['contact', 'contact_name', 'name', 'customer_contact', 'end_customer_contact_name', 
                          'subscription_contact_name', 'admin_name', 'primary_contact', 'first_name', 'last_name'],
            'normalized_fields' => ['contact_name', 'end_customer_contact_name', 'subscription_contact_name']
        ],
        
        // Product/Software names
        'product_name' => [
            'patterns' => ['product', 'product_name', 'software', 'license', 'offering', 'product_family',
                          'agreement_program_name', 'service', 'application', 'tool'],
            'normalized_fields' => ['product_name', 'subs_offeringName', 'agreement_program_name']
        ],
        
        // Subscription references/IDs
        'subscription_reference' => [
            'patterns' => ['reference', 'subscription_reference', 'serial', 'license_key', 'subscription_number', 
                          'subscription_id', 'agreement_number', 'contract_number', 'order_number', 'ref'],
            'normalized_fields' => ['subscription_reference', 'subs_subscriptionReferenceNumber', 'subscription_id']
        ],
        
        // Quantities
        'quantity' => [
            'patterns' => ['quantity', 'qty', 'licenses', 'seats', 'subscription_quantity', 'users', 'count'],
            'normalized_fields' => ['quantity', 'subs_quantity', 'subscription_quantity']
        ],
        
        // Start dates
        'start_date' => [
            'patterns' => ['start_date', 'date_start', 'purchase_date', 'agreement_start_date', 
                          'subscription_start_date', 'activation_date', 'begin_date'],
            'normalized_fields' => ['start_date', 'subs_startDate', 'agreement_start_date']
        ],
        
        // End/Expiry dates
        'end_date' => [
            'patterns' => ['end_date', 'expiry_date', 'renewal_date', 'date_end', 'agreement_end_date', 
                          'subscription_end_date', 'expiration_date', 'due_date'],
            'normalized_fields' => ['end_date', 'subs_endDate', 'agreement_end_date']
        ],
        
        // Status fields
        'status' => [
            'patterns' => ['status', 'subscription_status', 'agreement_status', 'state', 'active', 
                          'license_status', 'account_status'],
            'normalized_fields' => ['status', 'subs_status', 'subscription_status']
        ],
        
        // Address fields
        'address' => [
            'patterns' => ['address', 'address_1', 'street', 'end_customer_address_1', 'business_address'],
            'normalized_fields' => ['address', 'end_customer_address_1']
        ],
        
        'city' => [
            'patterns' => ['city', 'end_customer_city', 'town'],
            'normalized_fields' => ['city', 'end_customer_city']
        ],
        
        'state' => [
            'patterns' => ['state', 'province', 'region', 'end_customer_state'],
            'normalized_fields' => ['state', 'end_customer_state']
        ],
        
        'country' => [
            'patterns' => ['country', 'end_customer_country', 'nation'],
            'normalized_fields' => ['country', 'end_customer_country']
        ],
        
        'postal_code' => [
            'patterns' => ['zip', 'postal_code', 'zip_code', 'postcode', 'end_customer_zip_code'],
            'normalized_fields' => ['postal_code', 'end_customer_zip_code']
        ]
    ];
    
    /**
     * Normalize a CSV entry using the unified field mapping
     * 
     * @param array $entry Original CSV row data
     * @param string $source_table Table name for context
     * @return array Normalized entry with standardized field names
     */
    public static function normalize_entry(array $entry, string $source_table = ''): array {
        $normalized = [
            'id' => $entry['id'] ?? uniqid(),
            'source_table' => $source_table,
            'data_source' => 'csv_table'
        ];
        
        tcs_log("Normalizing entry from {$source_table} with " . count($entry) . " fields", self::$log_target);
        
        // Apply field mappings
        foreach ($entry as $key => $value) {
            $lower_key = strtolower(trim($key));
            
            // Find matching field mapping
            foreach (self::$field_mappings as $category => $mapping) {
                if (in_array($lower_key, $mapping['patterns'])) {
                    // Map to all normalized field names for this category
                    foreach ($mapping['normalized_fields'] as $normalized_field) {
                        $normalized[$normalized_field] = $value;
                    }
                    tcs_log("Mapped {$key} -> {$category} (" . implode(', ', $mapping['normalized_fields']) . ")", self::$log_target);
                    break;
                }
            }
            
            // Always keep original field name as well
            $normalized[$key] = $value;
        }
        
        // Apply post-processing
        $normalized = self::apply_post_processing($normalized);
        
        return $normalized;
    }
    
    /**
     * Apply post-processing logic (date parsing, status calculation, etc.)
     */
    private static function apply_post_processing(array $normalized): array {
        // Calculate expiration status and days remaining
        if (!empty($normalized['end_date']) || !empty($normalized['subs_endDate'])) {
            $end_date = $normalized['end_date'] ?? $normalized['subs_endDate'];
            
            $end_timestamp = self::parse_date($end_date);
            if ($end_timestamp) {
                $now = time();
                $days_diff = ($end_timestamp - $now) / (60 * 60 * 24);
                $normalized['subs_enddatediff'] = round($days_diff);
                
                // Override status if expired
                if ($days_diff < 0) {
                    $normalized['subs_status'] = 'EXPIRED';
                    $normalized['status'] = 'EXPIRED';
                } elseif ($days_diff <= 30) {
                    // Keep original status but note it's expiring soon
                    if (empty($normalized['subs_status']) || $normalized['subs_status'] === 'Unknown') {
                        $normalized['subs_status'] = 'EXPIRING';
                    }
                }
                
                tcs_log("Date processing: {$end_date} -> {$days_diff} days remaining", self::$log_target);
            }
        }
        
        return $normalized;
    }
    
    /**
     * Parse date from various formats
     */
    private static function parse_date(string $date_string): ?int {
        if (empty($date_string)) return null;
        
        $formats = ['Y-m-d', 'd/m/Y', 'm/d/Y', 'Y-m-d H:i:s', 'd-m-Y', 'm-d-Y'];
        
        foreach ($formats as $format) {
            $date_obj = \DateTime::createFromFormat($format, $date_string);
            if ($date_obj !== false) {
                return $date_obj->getTimestamp();
            }
        }
        
        // Try strtotime as fallback
        $timestamp = strtotime($date_string);
        return $timestamp !== false ? $timestamp : null;
    }
    
    /**
     * Check if a table contains subscription-related data based on field analysis
     */
    public static function contains_subscription_data(string $table_name, array $available_fields = []): bool {
        if (empty($available_fields)) {
            // Get fields from table if not provided
            try {
                $columns_query = "SHOW COLUMNS FROM `{$table_name}`";
                $columns = tcs_db_query($columns_query) ?: [];
                $available_fields = array_column($columns, 'Field');
            } catch (\Exception $e) {
                return false;
            }
        }
        
        $field_names = array_map('strtolower', $available_fields);
        
        // Look for subscription-related keywords
        $subscription_indicators = [
            'subscription', 'license', 'agreement', 'contract', 'product', 'software',
            'email', 'company', 'customer', 'client', 'end_date', 'expiry', 'quantity'
        ];
        
        $matches = 0;
        foreach ($subscription_indicators as $indicator) {
            foreach ($field_names as $field_name) {
                if (strpos($field_name, $indicator) !== false) {
                    $matches++;
                    break;
                }
            }
        }
        
        // Consider it subscription data if it has 3+ relevant fields
        $has_subscription_data = $matches >= 3;
        
        tcs_log("Table {$table_name} subscription analysis: {$matches} matches, result: " . ($has_subscription_data ? 'YES' : 'NO'), self::$log_target);
        
        return $has_subscription_data;
    }
    
    /**
     * Get field mapping suggestions for a given set of column names
     */
    public static function suggest_field_mappings(array $column_names): array {
        $suggestions = [];
        
        foreach ($column_names as $column) {
            $lower_column = strtolower(trim($column));
            
            foreach (self::$field_mappings as $category => $mapping) {
                if (in_array($lower_column, $mapping['patterns'])) {
                    $suggestions[$column] = [
                        'category' => $category,
                        'normalized_fields' => $mapping['normalized_fields'],
                        'confidence' => 100 // Exact match
                    ];
                    break;
                }
            }
            
            // If no exact match, try partial matching
            if (!isset($suggestions[$column])) {
                foreach (self::$field_mappings as $category => $mapping) {
                    foreach ($mapping['patterns'] as $pattern) {
                        if (strpos($lower_column, $pattern) !== false || strpos($pattern, $lower_column) !== false) {
                            $suggestions[$column] = [
                                'category' => $category,
                                'normalized_fields' => $mapping['normalized_fields'],
                                'confidence' => 75 // Partial match
                            ];
                            break 2;
                        }
                    }
                }
            }
        }
        
        return $suggestions;
    }
}
